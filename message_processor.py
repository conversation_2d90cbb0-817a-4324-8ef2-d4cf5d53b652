from llm_handler import get_llm, load_llm_configs
from whatsapp_handler import WhatsA<PERSON><PERSON><PERSON><PERSON>, load_whatsapp_config

class MessageProcessor:
    def __init__(self):
        self.llm_configs = load_llm_configs()
        self.llms = [get_llm(config) for config in self.llm_configs]
        self.whatsapp_config = load_whatsapp_config()
        self.whatsapp_handler = WhatsAppHandler(self.whatsapp_config)

    def process_message(self, message_data):
        # This is a simplified example. In a real application, you would
        # parse the message_data to get the sender's number and the message content.
        sender_id = message_data.get('from')
        message_text = message_data.get('text', {}).get('body')

        if not sender_id or not message_text:
            print("Invalid message data")
            return

        # For simplicity, we'll just use the first enabled LLM
        if self.llms:
            llm = self.llms[0]
            response_text = llm.generate_response(message_text)
            if response_text:
                self.whatsapp_handler.send_message(sender_id, response_text)
        else:
            print("No enabled LLMs to generate a response.")