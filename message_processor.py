from llm_handler import get_llm, load_llm_configs
from whatsapp_handler import WhatsApp<PERSON><PERSON><PERSON>, load_whatsapp_config
import logging
import time
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class MessageProcessor:
    def __init__(self):
        try:
            self.llm_configs = load_llm_configs()
            self.llms = []

            # Initialize LLMs with error handling
            for config in self.llm_configs:
                try:
                    llm = get_llm(config)
                    self.llms.append(llm)
                    logger.info(f"Successfully initialized {config['name']} LLM")
                except Exception as e:
                    logger.error(f"Failed to initialize {config['name']} LLM: {e}")

            if not self.llms:
                raise ValueError("No LLMs could be initialized")

            self.whatsapp_config = load_whatsapp_config()
            self.whatsapp_handler = WhatsAppHandler(self.whatsapp_config)
            logger.info("MessageProcessor initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize MessageProcessor: {e}")
            raise

    def extract_message_content(self, message_data: Dict[Any, Any]) -> Optional[tuple]:
        """Extract sender ID and message text from WhatsApp message data."""
        try:
            sender_id = message_data.get('from')

            # Handle different message types
            message_text = None
            if 'text' in message_data:
                message_text = message_data['text'].get('body')
            elif 'interactive' in message_data:
                # Handle interactive messages (buttons, lists, etc.)
                interactive = message_data['interactive']
                if interactive.get('type') == 'button_reply':
                    message_text = interactive['button_reply'].get('title')
                elif interactive.get('type') == 'list_reply':
                    message_text = interactive['list_reply'].get('title')

            if not sender_id or not message_text:
                logger.warning(f"Invalid message data: sender_id={sender_id}, message_text={message_text}")
                return None

            return sender_id, message_text.strip()

        except Exception as e:
            logger.error(f"Error extracting message content: {e}")
            return None

    def generate_response_with_fallback(self, message_text: str) -> Optional[str]:
        """Try to generate response using available LLMs with fallback mechanism."""
        for i, llm in enumerate(self.llms):
            try:
                logger.info(f"Attempting to generate response using {llm.config['name']} (attempt {i+1}/{len(self.llms)})")
                response = llm.generate_response(message_text)

                if response and response.strip():
                    logger.info(f"Successfully generated response using {llm.config['name']}")
                    return response.strip()
                else:
                    logger.warning(f"{llm.config['name']} returned empty response")

            except Exception as e:
                logger.error(f"Error generating response with {llm.config['name']}: {e}")
                continue

        # If all LLMs fail, return a fallback message
        logger.error("All LLMs failed to generate response, using fallback")
        return "I'm sorry, I'm having trouble processing your message right now. Please try again later."

    def process_message(self, message_data: Dict[Any, Any]) -> bool:
        """Process incoming WhatsApp message with comprehensive error handling."""
        try:
            logger.info(f"Processing message: {message_data}")

            # Extract message content
            extracted = self.extract_message_content(message_data)
            if not extracted:
                logger.warning("Could not extract valid message content")
                return False

            sender_id, message_text = extracted
            logger.info(f"Message from {sender_id}: {message_text}")

            # Generate response with fallback
            response_text = self.generate_response_with_fallback(message_text)

            if not response_text:
                logger.error("Failed to generate any response")
                return False

            # Send response with retry mechanism
            success = self.send_response_with_retry(sender_id, response_text)

            if success:
                logger.info(f"Successfully processed and responded to message from {sender_id}")
                return True
            else:
                logger.error(f"Failed to send response to {sender_id}")
                return False

        except Exception as e:
            logger.error(f"Unexpected error processing message: {e}")
            return False

    def send_response_with_retry(self, sender_id: str, response_text: str, max_retries: int = 3) -> bool:
        """Send response with retry mechanism."""
        for attempt in range(max_retries):
            try:
                self.whatsapp_handler.send_message(sender_id, response_text)
                return True
            except Exception as e:
                logger.warning(f"Failed to send message (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                continue

        logger.error(f"Failed to send message after {max_retries} attempts")
        return False