#!/usr/bin/env python3
"""
Configuration validation script for WhatsApp Bot
Checks if all required configurations are properly set
"""

import json
import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def validate_config():
    """Validate the configuration file."""
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError:
        logger.error("config.json file not found!")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in config.json: {e}")
        return False
    
    issues = []
    warnings = []
    
    # Validate app settings
    app_settings = config.get('app_settings', {})
    if not isinstance(app_settings.get('port'), int):
        issues.append("app_settings.port must be an integer")
    
    # Validate WhatsApp configuration
    whatsapp = config.get('whatsapp', {})
    if not whatsapp:
        issues.append("whatsapp configuration section is missing")
    else:
        if not whatsapp.get('api_token') or whatsapp['api_token'].startswith('YOUR_'):
            warnings.append("WhatsApp API token is not configured (using placeholder)")
        
        if not whatsapp.get('business_id') or whatsapp['business_id'].startswith('YOUR_'):
            warnings.append("WhatsApp Business ID is not configured (using placeholder)")
    
    # Validate LLM configurations
    llms = config.get('llms', [])
    if not llms:
        issues.append("No LLM configurations found")
    else:
        enabled_llms = [llm for llm in llms if llm.get('enabled')]
        if not enabled_llms:
            issues.append("No LLMs are enabled")
        
        for llm in llms:
            name = llm.get('name', 'unnamed')
            
            if not llm.get('name'):
                issues.append(f"LLM configuration missing 'name' field")
                continue
            
            if llm.get('enabled'):
                if name != 'ollama':  # Ollama uses api_url instead of api_key
                    if not llm.get('api_key') or llm['api_key'].startswith('YOUR_'):
                        warnings.append(f"{name} LLM is enabled but API key is not configured")
                else:
                    if not llm.get('api_url'):
                        warnings.append(f"{name} LLM is enabled but API URL is not configured")
    
    # Validate logging configuration
    logging_config = config.get('logging', {})
    valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    if logging_config.get('level') not in valid_levels:
        issues.append(f"Invalid logging level. Must be one of: {valid_levels}")
    
    # Print results
    if issues:
        logger.error("Configuration validation failed!")
        for issue in issues:
            logger.error(f"  ❌ {issue}")
    
    if warnings:
        logger.warning("Configuration warnings:")
        for warning in warnings:
            logger.warning(f"  ⚠️  {warning}")
    
    if not issues and not warnings:
        logger.info("✅ Configuration is valid and complete!")
        return True
    elif not issues:
        logger.info("✅ Configuration is valid but has warnings.")
        return True
    else:
        return False

def print_config_template():
    """Print a template configuration for reference."""
    template = {
        "app_settings": {
            "debug": True,
            "port": 5000,
            "verify_token": "YOUR_VERIFY_TOKEN"
        },
        "whatsapp": {
            "api_token": "YOUR_WHATSAPP_API_TOKEN",
            "business_id": "YOUR_WHATSAPP_BUSINESS_ID",
            "api_version": "v19.0"
        },
        "llms": [
            {
                "name": "openai",
                "api_key": "YOUR_OPENAI_API_KEY",
                "enabled": False,
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.7,
                "priority": 1
            }
        ],
        "logging": {
            "level": "INFO",
            "file": "whatsapp_bot.log",
            "max_file_size": "10MB",
            "backup_count": 5
        }
    }
    
    print("\n" + "="*50)
    print("CONFIGURATION TEMPLATE")
    print("="*50)
    print(json.dumps(template, indent=2))
    print("="*50)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--template":
        print_config_template()
        sys.exit(0)
    
    success = validate_config()
    
    if not success:
        print("\nTo see a configuration template, run:")
        print("python validate_config.py --template")
    
    sys.exit(0 if success else 1)
