#!/usr/bin/env python3
"""
Setup script for WhatsApp Bot
Helps users configure and test their bot setup
"""

import json
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        logger.error("Python 3.7 or higher is required")
        return False
    logger.info(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies."""
    logger.info("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install dependencies: {e}")
        return False

def validate_configuration():
    """Validate the configuration."""
    logger.info("Validating configuration...")
    try:
        result = subprocess.run([sys.executable, "validate_config.py"], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Configuration is valid")
            return True
        else:
            logger.error("❌ Configuration validation failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        logger.error(f"❌ Error validating configuration: {e}")
        return False

def run_tests():
    """Run the test suite."""
    logger.info("Running test suite...")
    try:
        result = subprocess.run([sys.executable, "test_whatsapp_bot.py"], capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            logger.info("✅ All tests passed")
            return True
        else:
            logger.warning("⚠️  Some tests failed")
            return False
    except Exception as e:
        logger.error(f"❌ Error running tests: {e}")
        return False

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\nNext steps:")
    print("1. Configure your API keys in config.json:")
    print("   - WhatsApp Business API token and business ID")
    print("   - LLM API keys (OpenAI, Claude, etc.)")
    print("\n2. Set up your WhatsApp webhook:")
    print("   - Start the bot: python main.py")
    print("   - Use ngrok or similar to expose your local server")
    print("   - Configure webhook URL in Facebook Developer Console")
    print("\n3. Test your bot:")
    print("   - Send a message to your WhatsApp Business number")
    print("   - Check logs for any issues")
    print("\n4. Monitor logs:")
    print("   - Check whatsapp_bot.log for detailed logs")
    print("   - Adjust logging level in config.json if needed")
    print("\nFor help, check the documentation or run:")
    print("  python validate_config.py --template")
    print("="*60)

def main():
    """Main setup function."""
    print("🚀 WhatsApp Bot Setup")
    print("="*30)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Validating configuration", validate_configuration),
        ("Running tests", run_tests),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            logger.error(f"Setup failed at step: {step_name}")
            sys.exit(1)
    
    print_next_steps()

if __name__ == "__main__":
    main()
