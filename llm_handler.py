import json
import requests
from openai import OpenAI

class LLM:
    def __init__(self, config):
        self.config = config

    def generate_response(self, prompt):
        raise NotImplementedError

class OpenAI_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.client = OpenAI(api_key=self.config['api_key'])

    def generate_response(self, prompt):
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"Error generating response from OpenAI: {e}")
            return None

# Add other LLM classes here (Deepseek, OpenRouter, Claude, Gemini, Mistral, Ollama)

def get_llm(config):
    llm_name = config['name']
    if llm_name == 'openai':
        return OpenAI_LLM(config)
    # Add other LLM initializations here
    else:
        raise ValueError(f"Unknown LLM: {llm_name}")

def load_llm_configs():
    with open('config.json', 'r') as f:
        config = json.load(f)
    return [llm for llm in config['llms'] if llm['enabled']]