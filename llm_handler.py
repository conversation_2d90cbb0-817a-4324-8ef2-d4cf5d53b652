import json
import requests
from openai import OpenAI
import anthropic
import google.generativeai as genai
from mistralai import Mistral
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class LLM:
    def __init__(self, config):
        self.config = config

    def generate_response(self, prompt: str) -> Optional[str]:
        """Generate response from the LLM. Should be implemented by subclasses."""
        raise NotImplementedError

class OpenAI_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.client = OpenAI(api_key=self.config['api_key'])

    def generate_response(self, prompt: str) -> Optional[str]:
        if not prompt or not prompt.strip():
            logger.warning("Empty prompt provided to OpenAI")
            return None

        try:
            logger.debug(f"Sending request to OpenAI with model {self.config.get('model', 'gpt-3.5-turbo')}")
            response = self.client.chat.completions.create(
                model=self.config.get('model', 'gpt-3.5-turbo'),
                messages=[{"role": "user", "content": prompt.strip()}],
                max_tokens=self.config.get('max_tokens', 1000),
                temperature=self.config.get('temperature', 0.7),
                timeout=30
            )

            if response.choices and response.choices[0].message.content:
                return response.choices[0].message.content.strip()
            else:
                logger.warning("OpenAI returned empty response")
                return None

        except Exception as e:
            logger.error(f"Error generating response from OpenAI: {e}")
            return None

class Deepseek_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.client = OpenAI(
            api_key=self.config['api_key'],
            base_url="https://api.deepseek.com"
        )

    def generate_response(self, prompt: str) -> Optional[str]:
        if not prompt or not prompt.strip():
            logger.warning("Empty prompt provided to Deepseek")
            return None

        try:
            logger.debug(f"Sending request to Deepseek with model {self.config.get('model', 'deepseek-chat')}")
            response = self.client.chat.completions.create(
                model=self.config.get('model', 'deepseek-chat'),
                messages=[{"role": "user", "content": prompt.strip()}],
                max_tokens=self.config.get('max_tokens', 1000),
                temperature=self.config.get('temperature', 0.7),
                timeout=30
            )

            if response.choices and response.choices[0].message.content:
                return response.choices[0].message.content.strip()
            else:
                logger.warning("Deepseek returned empty response")
                return None

        except Exception as e:
            logger.error(f"Error generating response from Deepseek: {e}")
            return None

class OpenRouter_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.client = OpenAI(
            api_key=self.config['api_key'],
            base_url="https://openrouter.ai/api/v1"
        )

    def generate_response(self, prompt):
        try:
            response = self.client.chat.completions.create(
                model="meta-llama/llama-3.1-8b-instruct:free",  # Free model
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"Error generating response from OpenRouter: {e}")
            return None

class Claude_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.client = anthropic.Anthropic(api_key=self.config['api_key'])

    def generate_response(self, prompt):
        try:
            response = self.client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=1000,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            print(f"Error generating response from Claude: {e}")
            return None

class Gemini_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        genai.configure(api_key=self.config['api_key'])
        self.model = genai.GenerativeModel('gemini-pro')

    def generate_response(self, prompt):
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Error generating response from Gemini: {e}")
            return None

class Mistral_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.client = Mistral(api_key=self.config['api_key'])

    def generate_response(self, prompt: str) -> Optional[str]:
        if not prompt or not prompt.strip():
            logger.warning("Empty prompt provided to Mistral")
            return None

        try:
            logger.debug(f"Sending request to Mistral with model {self.config.get('model', 'mistral-tiny')}")
            response = self.client.chat.complete(
                model=self.config.get('model', 'mistral-tiny'),
                messages=[{"role": "user", "content": prompt.strip()}],
                max_tokens=self.config.get('max_tokens', 1000),
                temperature=self.config.get('temperature', 0.7)
            )

            if response.choices and response.choices[0].message.content:
                return response.choices[0].message.content.strip()
            else:
                logger.warning("Mistral returned empty response")
                return None

        except Exception as e:
            logger.error(f"Error generating response from Mistral: {e}")
            return None

class Ollama_LLM(LLM):
    def __init__(self, config):
        super().__init__(config)
        self.api_url = config.get('api_url', 'http://localhost:11434')

    def generate_response(self, prompt):
        try:
            response = requests.post(
                f"{self.api_url}/api/generate",
                json={
                    "model": "llama2",  # Default model, can be configured
                    "prompt": prompt,
                    "stream": False
                }
            )
            response.raise_for_status()
            return response.json().get('response', '')
        except Exception as e:
            print(f"Error generating response from Ollama: {e}")
            return None

def get_llm(config):
    llm_name = config['name']
    if llm_name == 'openai':
        return OpenAI_LLM(config)
    elif llm_name == 'deepseek':
        return Deepseek_LLM(config)
    elif llm_name == 'openrouter':
        return OpenRouter_LLM(config)
    elif llm_name == 'claude':
        return Claude_LLM(config)
    elif llm_name == 'gemini':
        return Gemini_LLM(config)
    elif llm_name == 'mistral':
        return Mistral_LLM(config)
    elif llm_name == 'ollama':
        return Ollama_LLM(config)
    else:
        raise ValueError(f"Unknown LLM: {llm_name}")

def load_llm_configs():
    with open('config.json', 'r') as f:
        config = json.load(f)

    enabled_llms = [llm for llm in config['llms'] if llm['enabled']]

    # Validate that at least one LLM is enabled
    if not enabled_llms:
        raise ValueError("No LLMs are enabled in configuration")

    # Sort by priority (lower number = higher priority)
    enabled_llms.sort(key=lambda x: x.get('priority', 999))

    return enabled_llms