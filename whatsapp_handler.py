import json
import requests
import logging

logger = logging.getLogger(__name__)

class WhatsAppHandler:
    def __init__(self, config):
        self.config = config
        api_version = config.get('api_version', 'v19.0')
        self.api_url = f"https://graph.facebook.com/{api_version}/{self.config['business_id']}/messages"
        self.headers = {
            "Authorization": f"Bearer {self.config['api_token']}",
            "Content-Type": "application/json",
        }

    def send_message(self, to: str, message: str) -> bool:
        """Send a WhatsApp message with comprehensive error handling."""
        if not to or not message:
            logger.error("Invalid parameters: 'to' and 'message' are required")
            raise ValueError("Both 'to' and 'message' parameters are required")

        # Truncate message if too long (WhatsApp limit is 4096 characters)
        if len(message) > 4096:
            message = message[:4093] + "..."
            logger.warning(f"Message truncated to fit WhatsApp limit")

        data = {
            "messaging_product": "whatsapp",
            "to": to,
            "text": {"body": message},
        }

        try:
            logger.info(f"Sending message to {to}")
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=data,
                timeout=30  # Add timeout
            )

            # Check for HTTP errors
            response.raise_for_status()

            # Check WhatsApp API response
            response_data = response.json()
            if 'error' in response_data:
                error_info = response_data['error']
                logger.error(f"WhatsApp API error: {error_info}")
                raise requests.exceptions.RequestException(f"WhatsApp API error: {error_info}")

            logger.info(f"Message successfully sent to {to}")
            return True

        except requests.exceptions.Timeout:
            logger.error(f"Timeout sending message to {to}")
            raise
        except requests.exceptions.ConnectionError:
            logger.error(f"Connection error sending message to {to}")
            raise
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error sending message to {to}: {e}")
            if response.status_code == 401:
                raise requests.exceptions.RequestException("Invalid WhatsApp API token")
            elif response.status_code == 403:
                raise requests.exceptions.RequestException("WhatsApp API access forbidden")
            elif response.status_code == 429:
                raise requests.exceptions.RequestException("WhatsApp API rate limit exceeded")
            else:
                raise
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error sending message to {to}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error sending message to {to}: {e}")
            raise

def load_whatsapp_config():
    with open('config.json', 'r') as f:
        config = json.load(f)
    whatsapp_config = config.get('whatsapp', {})

    # Validate required fields
    required_fields = ['api_token', 'business_id']
    for field in required_fields:
        if not whatsapp_config.get(field) or whatsapp_config[field].startswith('YOUR_'):
            raise ValueError(f"WhatsApp {field} not configured properly in config.json")

    return whatsapp_config