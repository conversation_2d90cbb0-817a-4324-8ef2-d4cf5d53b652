import json
import requests

class WhatsAppHandler:
    def __init__(self, config):
        self.config = config
        self.api_url = f"https://graph.facebook.com/v19.0/{self.config['whatsapp_business_id']}/messages"
        self.headers = {
            "Authorization": f"Bearer {self.config['whatsapp_api_token']}",
            "Content-Type": "application/json",
        }

    def send_message(self, to, message):
        data = {
            "messaging_product": "whatsapp",
            "to": to,
            "text": {"body": message},
        }
        try:
            response = requests.post(self.api_url, headers=self.headers, json=data)
            response.raise_for_status()
            print(f"Message sent to {to}: {message}")
        except requests.exceptions.RequestException as e:
            print(f"Error sending message to {to}: {e}")

def load_whatsapp_config():
    with open('config.json', 'r') as f:
        config = json.load(f)
    return {
        'whatsapp_api_token': config['whatsapp_api_token'],
        'whatsapp_business_id': config['whatsapp_business_id']
    }