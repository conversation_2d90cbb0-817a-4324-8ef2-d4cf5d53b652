#!/usr/bin/env python3
"""
Test script for WhatsApp Bot functionality
Tests LLM integrations, configuration loading, and message processing
"""

import json
import sys
import logging
from unittest.mock import Mock, patch
import requests

# Setup logging for tests
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_loading():
    """Test configuration loading and validation."""
    logger.info("Testing configuration loading...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Test required sections
        required_sections = ['app_settings', 'whatsapp', 'llms', 'logging']
        for section in required_sections:
            if section not in config:
                logger.error(f"Missing required config section: {section}")
                return False
        
        # Test LLM configurations
        enabled_llms = [llm for llm in config['llms'] if llm['enabled']]
        if not enabled_llms:
            logger.error("No LLMs are enabled in configuration")
            return False
        
        logger.info(f"Found {len(enabled_llms)} enabled LLMs: {[llm['name'] for llm in enabled_llms]}")
        
        # Test WhatsApp config
        whatsapp_config = config['whatsapp']
        if whatsapp_config['api_token'].startswith('YOUR_') or whatsapp_config['business_id'].startswith('YOUR_'):
            logger.warning("WhatsApp credentials are not configured (using placeholder values)")
        
        logger.info("✅ Configuration loading test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration loading test failed: {e}")
        return False

def test_llm_handler_imports():
    """Test that all LLM handler imports work correctly."""
    logger.info("Testing LLM handler imports...")
    
    try:
        from llm_handler import get_llm, load_llm_configs
        logger.info("✅ LLM handler imports successful")
        return True
    except ImportError as e:
        logger.error(f"❌ LLM handler import failed: {e}")
        logger.info("This might be due to missing dependencies. Install them with: pip install -r requirements.txt")
        return False

def test_llm_initialization():
    """Test LLM initialization with current configuration."""
    logger.info("Testing LLM initialization...")
    
    try:
        from llm_handler import get_llm, load_llm_configs
        
        configs = load_llm_configs()
        initialized_llms = []
        
        for config in configs:
            try:
                llm = get_llm(config)
                initialized_llms.append(config['name'])
                logger.info(f"✅ Successfully initialized {config['name']} LLM")
            except Exception as e:
                logger.warning(f"⚠️  Failed to initialize {config['name']} LLM: {e}")
        
        if initialized_llms:
            logger.info(f"✅ LLM initialization test passed. Working LLMs: {initialized_llms}")
            return True
        else:
            logger.error("❌ No LLMs could be initialized")
            return False
            
    except Exception as e:
        logger.error(f"❌ LLM initialization test failed: {e}")
        return False

def test_message_processor():
    """Test message processor initialization."""
    logger.info("Testing message processor...")
    
    try:
        # Mock the WhatsApp config to avoid requiring real credentials
        with patch('message_processor.load_whatsapp_config') as mock_whatsapp_config:
            mock_whatsapp_config.return_value = {
                'api_token': 'test_token',
                'business_id': 'test_business_id',
                'api_version': 'v19.0'
            }
            
            from message_processor import MessageProcessor
            processor = MessageProcessor()
            
            logger.info("✅ Message processor initialization successful")
            return True
            
    except Exception as e:
        logger.error(f"❌ Message processor test failed: {e}")
        return False

def test_mock_message_processing():
    """Test message processing with mock data."""
    logger.info("Testing message processing with mock data...")
    
    try:
        # Mock WhatsApp handler to avoid sending real messages
        with patch('message_processor.load_whatsapp_config') as mock_whatsapp_config, \
             patch('whatsapp_handler.WhatsAppHandler') as mock_handler:
            
            mock_whatsapp_config.return_value = {
                'api_token': 'test_token',
                'business_id': 'test_business_id',
                'api_version': 'v19.0'
            }
            
            # Create mock handler instance
            mock_handler_instance = Mock()
            mock_handler_instance.send_message.return_value = True
            mock_handler.return_value = mock_handler_instance
            
            from message_processor import MessageProcessor
            processor = MessageProcessor()
            
            # Test message data
            test_message = {
                'from': '1234567890',
                'text': {'body': 'Hello, this is a test message'}
            }
            
            result = processor.process_message(test_message)
            
            if result:
                logger.info("✅ Mock message processing test passed")
                return True
            else:
                logger.error("❌ Mock message processing returned False")
                return False
                
    except Exception as e:
        logger.error(f"❌ Mock message processing test failed: {e}")
        return False

def test_webhook_endpoints():
    """Test webhook endpoints (requires running server)."""
    logger.info("Testing webhook endpoints...")
    
    try:
        # Test if server is running
        response = requests.get('http://localhost:5000/webhook?hub.mode=subscribe&hub.verify_token=test&hub.challenge=test_challenge', timeout=5)
        
        if response.status_code == 200:
            logger.info("✅ Webhook GET endpoint is responding")
        else:
            logger.warning(f"⚠️  Webhook GET endpoint returned status {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        logger.warning("⚠️  Server is not running. Start it with: python main.py")
        return False
    except Exception as e:
        logger.error(f"❌ Webhook endpoint test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and return overall result."""
    logger.info("🚀 Starting WhatsApp Bot Test Suite")
    logger.info("=" * 50)
    
    tests = [
        ("Configuration Loading", test_config_loading),
        ("LLM Handler Imports", test_llm_handler_imports),
        ("LLM Initialization", test_llm_initialization),
        ("Message Processor", test_message_processor),
        ("Mock Message Processing", test_mock_message_processing),
        ("Webhook Endpoints", test_webhook_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 30)
        
        if test_func():
            passed += 1
        
        logger.info("")
    
    logger.info("=" * 50)
    logger.info(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Your WhatsApp bot is ready.")
    else:
        logger.warning(f"⚠️  {total - passed} tests failed. Check the logs above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
