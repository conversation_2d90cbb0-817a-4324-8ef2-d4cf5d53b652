from flask import Flask, request, jsonify
from message_processor import MessageProcessor
import json

app = Flask(__name__)
message_processor = MessageProcessor()

@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.get_json()
    if data:
        # The structure of the WhatsApp webhook payload can be complex.
        # This is a simplified example.
        if 'entry' in data and data['entry']:
            for entry in data['entry']:
                if 'changes' in entry:
                    for change in entry['changes']:
                        if 'value' in change and 'messages' in change['value']:
                            for message in change['value']['messages']:
                                message_processor.process_message(message)
    return jsonify({'status': 'ok'})

@app.route('/webhook', methods=['GET'])
def verify_webhook():
    # This is part of the webhook verification process with Facebook
    verify_token = "YOUR_VERIFY_TOKEN" # You need to set this token in your Facebook App
    mode = request.args.get('hub.mode')
    token = request.args.get('hub.verify_token')
    challenge = request.args.get('hub.challenge')

    if mode and token:
        if mode == 'subscribe' and token == verify_token:
            return challenge
    return 'Failed validation. Make sure the validation tokens match.'

if __name__ == '__main__':
    app.run(port=5000, debug=True)