from flask import Flask, request, jsonify
from message_processor import MessageProcessor
import json
import logging

# Load configuration
with open('config.json', 'r') as f:
    config = json.load(f)

app = Flask(__name__)
app.config['DEBUG'] = config.get('app_settings', {}).get('debug', True)

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.get('logging', {}).get('level', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.get('logging', {}).get('file', 'whatsapp_bot.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

message_processor = MessageProcessor()

@app.route('/webhook', methods=['POST'])
def webhook():
    try:
        data = request.get_json()
        logger.info(f"Received webhook data: {data}")

        if data and 'entry' in data and data['entry']:
            for entry in data['entry']:
                if 'changes' in entry:
                    for change in entry['changes']:
                        if 'value' in change and 'messages' in change['value']:
                            for message in change['value']['messages']:
                                logger.info(f"Processing message: {message}")
                                message_processor.process_message(message)

        return jsonify({'status': 'ok'})
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/webhook', methods=['GET'])
def verify_webhook():
    try:
        # This is part of the webhook verification process with Facebook
        verify_token = config.get('app_settings', {}).get('verify_token', 'YOUR_VERIFY_TOKEN')
        mode = request.args.get('hub.mode')
        token = request.args.get('hub.verify_token')
        challenge = request.args.get('hub.challenge')

        logger.info(f"Webhook verification attempt: mode={mode}, token={token}")

        if mode and token:
            if mode == 'subscribe' and token == verify_token:
                logger.info("Webhook verification successful")
                return challenge

        logger.warning("Webhook verification failed")
        return 'Failed validation. Make sure the validation tokens match.', 403
    except Exception as e:
        logger.error(f"Error in webhook verification: {e}")
        return 'Verification error', 500

if __name__ == '__main__':
    port = config.get('app_settings', {}).get('port', 5000)
    debug = config.get('app_settings', {}).get('debug', True)
    logger.info(f"Starting WhatsApp bot on port {port}")
    app.run(port=port, debug=debug)