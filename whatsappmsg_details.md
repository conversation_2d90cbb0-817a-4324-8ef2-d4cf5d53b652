Objective:
Develop a Python application that integrates with the WhatsApp API to read incoming messages, analyze them using multiple Language Model Models (LLMs), and compose appropriate responses. The application should support multiple LLMs including OpenAI, Deepseek, OpenRouter, Claude, Gemini, Mistral, and a locally installed model like Ollama, with the flexibility to add custom LLMs.

Requirements:
1. WhatsApp Integration:
   - Access WhatsApp messages using the official WhatsApp Business API.
   - Ensure compliance with WhatsApp's terms of service.

2. LLM Integration:
   - Integrate with multiple LLMs:
     - OpenAI
     - Deepseek
     - OpenRouter
     - Claude
     - Gemini
     - Mistral
     - Local installation of Ollama
   - Provide an option to add and configure custom LLMs.

3. Message Processing:
   - Analyze the nature of received messages (e.g., query, feedback, support request).
   - Use LLMs to determine context and intent.

4. Response Generation:
   - Compose contextually appropriate responses.
   - Prioritize using free-tier resources where available.

5. Configuration:
   - Implement configuration management for API keys and settings.
   - Provide a user-friendly interface for managing LLMs and settings.

6. Error Handling:
   - Robust error handling and logging.
   - Graceful fallback mechanisms.

Steps to Implement:

1. Setup Environment:
   - Install necessary Python libraries (e.g., `requests`, `flask`, `openai`).
   - Set up virtual environment.

2. WhatsApp API Connection:
   - Use the WhatsApp Business API to set up webhook for incoming messages.
   - Implement message retrieval and parsing logic.

3. LLM API Integration:
   - Set up API clients for each LLM.
   - Implement a modular approach to add or remove LLMs easily.

4. Message Analysis:
   - Develop a function to classify the message type.
   - Implement logic to choose the appropriate LLM for response generation.

5. Response Composition:
   - Use selected LLM to generate a response.
   - Implement logic to handle multiple model outputs and select the best response.

6. Configuration Management:
   - Create a configuration file (e.g., `config.json`) to manage API keys and settings.
   - Build a simple UI or CLI to modify configurations.

7. Testing and Deployment:
   - Test each component independently.
   - Deploy on a server with access to WhatsApp API and LLMs.

8. Documentation:
   - Document code and provide a user guide for setup and operation.

Considerations:
- Ensure data privacy and secure handling of messages.
- Optimize for cost-effectiveness by using free tiers when possible.

Most Important: before creating any code, check my system, does it has all the required applications and libraries.