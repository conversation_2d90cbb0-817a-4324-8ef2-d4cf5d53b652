{"app_settings": {"debug": true, "port": 5000, "verify_token": "YOUR_VERIFY_TOKEN"}, "whatsapp": {"api_token": "EAA5Y4EJZAt1IBPOr3s8gFvVefDGASwmbMRGUxRGAOcx89bVZBLiXgv3MHqCJMojlvx1IXWiVcETCRSthzknsCS7Fl3bmcEWhk3E2KsiBaUO8Nt3C47me9uZCn52ZCK20eZCz8YHXzgaSqq7cgPX9BKwcZC7LsgvaW7rVvC8KKjrPXfH3ZClrFlN7scRldFiyZAe9jeEPNKTAXWjUHUKqmJUOT5fTXcQfrZAd6H0mzSCF1xtAZD", "business_id": "1170282088245353", "api_version": "v19.0"}, "llms": [{"name": "openai", "api_key": "YOUR_OPENAI_API_KEY", "enabled": false, "model": "gpt-3.5-turbo", "max_tokens": 1000, "temperature": 0.7, "priority": 1}, {"name": "deepseek", "api_key": "***********************************", "enabled": true, "model": "deepseek-chat", "max_tokens": 1000, "temperature": 0.7, "priority": 2}, {"name": "openrouter", "api_key": "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", "enabled": true, "model": "meta-llama/llama-3.1-8b-instruct:free", "max_tokens": 1000, "temperature": 0.7, "priority": 3}, {"name": "claude", "api_key": "YOUR_CLAUDE_API_KEY", "enabled": false, "model": "claude-3-haiku-20240307", "max_tokens": 1000, "temperature": 0.7, "priority": 4}, {"name": "gemini", "api_key": "YOUR_GEMINI_API_KEY", "enabled": false, "model": "gemini-pro", "max_tokens": 1000, "temperature": 0.7, "priority": 5}, {"name": "mistral", "api_key": "YOUR_MISTRAL_API_KEY", "enabled": false, "model": "mistral-tiny", "max_tokens": 1000, "temperature": 0.7, "priority": 6}, {"name": "ollama", "api_url": "http://localhost:11434", "enabled": false, "model": "llama2", "max_tokens": 1000, "temperature": 0.7, "priority": 7}], "logging": {"level": "INFO", "file": "whatsapp_bot.log", "max_file_size": "10MB", "backup_count": 5}}